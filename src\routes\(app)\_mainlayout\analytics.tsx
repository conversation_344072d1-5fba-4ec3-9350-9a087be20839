import MobileSwitcher from "@/components/layout/main/mobile-switcher";
import { Navbar } from "@/components/layout/main/navbar";
import useMediaQuery from "@/hooks/use-media-query";
import { createFileRoute } from "@tanstack/react-router";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/charts";
import { useGetAnalytics } from "@/lib/queries/user.query";
import { AnalyticsEmptyState } from "@/components/common";

// Analytics-specific loading components
const LoadingBlock = ({ className = "" }: { className?: string }) => (
	<div className={`animate-pulse bg-slate-100 rounded-md ${className}`}></div>
);

const AnalyticsMainLoading = () => (
	<div className="mb-9 px-6 py-7 bg-white rounded-3xl border border-gray-300">
		{/* Header */}
		<div className="mb-6">
			<LoadingBlock className="h-4 w-48 mb-2.5" />
			<div className="flex items-baseline gap-2">
				<LoadingBlock className="h-8 w-16" />
				<LoadingBlock className="h-4 w-12" />
			</div>
		</div>

		{/* Main Content Grid */}
		<div className="grid grid-cols-1 lg:grid-cols-4 gap-9 lg:justify-between">
			{/* Left: Main Chart */}
			<div className="flex flex-col justify-between">
				<LoadingBlock className="w-[140px] h-[140px] rounded-full mx-auto mt-8 mb-10" />
				<div className="space-y-2">
					<div className="flex items-center justify-between">
						<div className="flex items-center gap-2">
							<LoadingBlock className="w-4 h-4 rounded-full" />
							<LoadingBlock className="h-4 w-24" />
						</div>
						<LoadingBlock className="h-4 w-8" />
					</div>
					<div className="flex items-center justify-between">
						<div className="flex items-center gap-2">
							<LoadingBlock className="w-4 h-4 rounded-full" />
							<LoadingBlock className="h-4 w-20" />
						</div>
						<LoadingBlock className="h-4 w-8" />
					</div>
				</div>
			</div>

			{/* MCQ's Type */}
			<div className="flex flex-col justify-between">
				<LoadingBlock className="h-4 w-20 mb-4" />
				<div className="space-y-2.5">
					{[...Array(2)].map((_, i) => (
						<div
							key={i}
							className="flex items-center justify-between py-2.5 px-5 bg-white border border-[#D2D5DA] rounded-lg shadow-sm"
						>
							<div className="flex flex-col">
								<LoadingBlock className="h-4 w-16 mb-1" />
								<div className="flex items-baseline gap-1">
									<LoadingBlock className="h-6 w-8" />
									<LoadingBlock className="h-3 w-8" />
								</div>
							</div>
							<LoadingBlock className="w-[63px] h-[63px] rounded-full" />
						</div>
					))}
				</div>
			</div>

			{/* Difficulty Type */}
			<div className="flex flex-col justify-between">
				<LoadingBlock className="h-4 w-24 mb-4" />
				<div className="space-y-2.5">
					{[...Array(3)].map((_, i) => (
						<div
							key={i}
							className="flex items-center justify-between py-2.5 px-5 bg-white border border-gray-200 rounded-lg shadow-sm"
						>
							<div className="flex flex-col">
								<LoadingBlock className="h-4 w-12 mb-1" />
								<div className="flex items-baseline gap-1">
									<LoadingBlock className="h-6 w-8" />
									<LoadingBlock className="h-3 w-8" />
								</div>
							</div>
							<LoadingBlock className="w-[63px] h-[63px] rounded-full" />
						</div>
					))}
				</div>
			</div>

			{/* Subject Wise */}
			<div className="flex flex-col justify-between">
				<LoadingBlock className="h-4 w-20 mb-4" />
				<div className="space-y-4">
					{[...Array(4)].map((_, i) => (
						<div key={i} className="space-y-2">
							<LoadingBlock className="h-4 w-16" />
							<LoadingBlock className="h-4 w-full" />
						</div>
					))}
				</div>
			</div>
		</div>
	</div>
);

const AnalyticsFutureLeftLoading = () => (
	<div className="mb-9 px-6 py-7 bg-white rounded-3xl border border-gray-300 flex flex-col">
		<div className="mb-6">
			<LoadingBlock className="h-4 w-32 mb-2.5" />
			<div className="flex items-baseline gap-2">
				<LoadingBlock className="h-8 w-16" />
				<LoadingBlock className="h-4 w-16" />
			</div>
		</div>
		<div className="h-full flex flex-col justify-between">
			<LoadingBlock className="w-[140px] h-[140px] rounded-full mx-auto mt-8 mb-10" />
			<div className="space-y-2">
				<div className="flex items-center justify-between">
					<div className="flex items-center gap-2">
						<LoadingBlock className="w-4 h-4 rounded-full" />
						<LoadingBlock className="h-4 w-24" />
					</div>
					<LoadingBlock className="h-4 w-8" />
				</div>
				<div className="flex items-center justify-between">
					<div className="flex items-center gap-2">
						<LoadingBlock className="w-4 h-4 rounded-full" />
						<LoadingBlock className="h-4 w-20" />
					</div>
					<LoadingBlock className="h-4 w-8" />
				</div>
			</div>
		</div>
	</div>
);

const AnalyticsFutureRightLoading = () => (
	<div className="mb-9 px-6 py-7 bg-white rounded-3xl border border-gray-300">
		<div className="mb-10">
			<LoadingBlock className="h-4 w-40 mb-2.5" />
			<div className="flex items-baseline gap-2">
				<LoadingBlock className="h-8 w-12" />
				<LoadingBlock className="h-4 w-20" />
			</div>
		</div>
		{/* Subject Wise Progress Bars */}
		<div className="space-y-4">
			{[...Array(4)].map((_, i) => (
				<div key={i} className="space-y-2">
					<LoadingBlock className="h-4 w-16" />
					<LoadingBlock className="h-4 w-full" />
				</div>
			))}
		</div>
	</div>
);

const Page = () => {
	const isDesktop = useMediaQuery("(min-width: 1024px)");

	// Fetch analytics data from API
	const {
		data: analyticsData,
		isLoading,
		error: analyticsError,
	} = useGetAnalytics();
	const analytics = analyticsData?.data?.data?.analytics;

	console.log(analytics, "________analytics");

	// Calculate analytics data based on API data
	const totalMCQsSolved = analytics?.mcqsSolvedCount || 0;
	const avgScore = analytics?.avgScorePerQuiz || 0;

	// const correctAnswers =
	// 	analytics?.correctAnswers || Math.round((totalMCQsSolved * avgScore) / 100);
	// const wrongAnswers =
	// 	analytics?.wrongAnswers || totalMCQsSolved - correctAnswers;

	// Generate a random number between 0 and totalMCQsSolved for correct answers
	const correctAnswers = Math.floor(Math.random() * (totalMCQsSolved + 1));
	const wrongAnswers = totalMCQsSolved - correctAnswers;

	// Determine if we should show empty state
	const hasAnalyticsData = !isLoading && !analyticsError && totalMCQsSolved > 0;

	// Analytics data using real API data where possible
	const totalAttemptedData = [
		{
			name: "Total",
			value: correctAnswers,
			fill: "rgba(89, 54, 205, 1)",
		},
		{
			name: "Remaining",
			value: wrongAnswers,
			fill: "rgba(89, 54, 205, 0.5)",
		},
	];

	// MCQ Type data from analytics API or fallback
	const mcqTypeData = analytics?.mcqTypeStats?.length
		? analytics.mcqTypeStats.map((item) => ({
				name: item.type,
				remaining: {
					value: item.attempted - item.correct,
					fill: "rgba(89, 54, 205, 1)",
				},
				total: {
					value: item.attempted,
					fill: "rgba(89, 54, 205, 0.5)",
				},
			}))
		: [
				{
					name: "Multiple Choice",
					remaining: {
						value: Math.floor(totalMCQsSolved * 0.3),
						fill: "rgba(89, 54, 205, 1)",
					},
					total: {
						value: Math.floor(totalMCQsSolved * 0.6),
						fill: "rgba(89, 54, 205, 0.5)",
					},
				},
				{
					name: "True/False",
					remaining: {
						value: Math.floor(totalMCQsSolved * 0.2),
						fill: "rgba(89, 54, 205, 1)",
					},
					total: {
						value: Math.floor(totalMCQsSolved * 0.4),
						fill: "rgba(89, 54, 205, 0.5)",
					},
				},
			];

	// Difficulty data from analytics API or fallback
	const difficultyTypeData = analytics?.difficultyStats?.length
		? analytics.difficultyStats.map((item) => ({
				name: item.difficulty,
				remaining: {
					value: item.attempted - item.correct,
					fill: "rgba(89, 54, 205, 1)",
				},
				total: {
					value: item.attempted,
					fill: "rgba(89, 54, 205, 0.5)",
				},
			}))
		: [
				{
					name: "Easy",
					remaining: {
						value: Math.floor(totalMCQsSolved * 0.2),
						fill: "rgba(89, 54, 205, 1)",
					},
					total: {
						value: Math.floor(totalMCQsSolved * 0.4),
						fill: "rgba(89, 54, 205, 0.5)",
					},
				},
				{
					name: "Medium",
					remaining: {
						value: Math.floor(totalMCQsSolved * 0.3),
						fill: "rgba(89, 54, 205, 1)",
					},
					total: {
						value: Math.floor(totalMCQsSolved * 0.4),
						fill: "rgba(89, 54, 205, 0.5)",
					},
				},
				{
					name: "Hard",
					remaining: {
						value: Math.floor(totalMCQsSolved * 0.4),
						fill: "rgba(89, 54, 205, 1)",
					},
					total: {
						value: Math.floor(totalMCQsSolved * 0.2),
						fill: "rgba(89, 54, 205, 0.5)",
					},
				},
			];

	// Subject-wise data from analytics API or default subjects
	const subjectWiseData = analytics?.subjectWiseStats?.length
		? analytics.subjectWiseStats.map((item) => ({
				name: item.subject,
				totalValue: 100,
				lightValue: item.accuracy || 0,
			}))
		: ["Maths", "Physics", "Chemistry", "English"].map((subject, index) => ({
				name: subject,
				lightValue: Math.round(20 + index * 5 + Math.random() * 15), // Fallback data
				totalValue: 100,
			}));

	// Future predictions with slight improvement over current performance
	const predictedCorrectAnswers = Math.round(correctAnswers * 1.1); // 10% improvement prediction
	const predictedWrongAnswers = Math.round(wrongAnswers * 0.9); // 10% reduction in wrong answers

	const futurePredictionData = [
		{
			name: "Total",
			value: predictedCorrectAnswers,
			fill: "rgba(89, 54, 205, 1)",
		},
		{
			name: "Remaining",
			value: predictedWrongAnswers,
			fill: "rgba(89, 54, 205, 0.5)",
		},
	];

	const subjectWisePredictionData = subjectWiseData.map((item) => ({
		name: item.name,
		lightValue: Math.min(Math.round(item.lightValue * 1.1), 95), // 10% improvement, capped at 95%
		totalValue: 100,
	}));

	return (
		<>
			{!isDesktop && (
				<>
					<Navbar />
					<MobileSwitcher />
				</>
			)}
			<main className="container mx-auto p-4 pb-20 lg:pb-4">
				{/* Header */}
				<div className="mb-7">
					<h2 className="font-bold text-2xl text-[#202224]">
						Recent Analytics
					</h2>
				</div>

				{/* Recent Analytics */}
				{isLoading ? (
					<AnalyticsMainLoading />
				) : analyticsError ? (
					<AnalyticsEmptyState variant="no-data" className="py-20" />
				) : !hasAnalyticsData ? (
					<AnalyticsEmptyState variant="no-tests-completed" />
				) : (
					<div className="mb-9 px-6 py-7 bg-white rounded-3xl border border-gray-300">
						{/* Header */}
						<div className="mb-6">
							<h3 className="text-sm font-bold text-gray-400 mb-2.5">
								TOTAL ATTEMPTED MCQ'S
							</h3>
							<p className="text-[32px] font-medium text-gray-700">
								{totalMCQsSolved}{" "}
								<span className="text-base font-medium text-gray-400">
									total
								</span>
							</p>
						</div>

						{/* Main Content Grid */}
						<div className="grid grid-cols-1 lg:grid-cols-4 gap-9 lg:justify-between">
							{/* Left: Pie Chart */}
							<PieChart
								data={totalAttemptedData}
								showLegend={true}
								innerRadius={0}
								outerRadius={70}
								className="flex flex-col justify-between"
							/>

							{/* MCQ's Type */}
							<div className="flex flex-col justify-between">
								<h3 className="text-sm font-semibold text-gray-600 mb-4">
									MCQ's Type
								</h3>
								<div className="space-y-2.5">
									{mcqTypeData.map((item, index) => (
										<div
											key={index}
											className="flex items-center justify-between py-2.5 px-5 bg-white border border-[#D2D5DA] rounded-lg shadow-sm"
										>
											<div className="flex flex-col">
												<span className="text-sm font-semibold text-gray-600 mb-1">
													{item.name}
												</span>
												<div className="flex items-baseline gap-1">
													<span className="text-3xl font-normal text-black">
														{item.total.value}
													</span>
													<span className="text-sm font-normal text-gray-400">
														total
													</span>
												</div>
											</div>
											<div className="flex-shrink-0">
												<DonutChart
													data={[
														{
															name: "Total",
															value: item.total.value,
															fill: item.total.fill,
														},
														{
															name: "Remaining",
															value: item.remaining.value,
															fill: item.remaining.fill,
														},
													]}
													showLegend={false}
													showTooltip={true}
													innerRadius={19}
													outerRadius={31}
													className="w-[63px] h-[63px]"
												/>
											</div>
										</div>
									))}
								</div>
							</div>

							{/* Difficulty Type */}
							<div className="flex flex-col justify-between">
								<h3 className="text-sm font-semibold text-gray-600 mb-4">
									Difficulty Type
								</h3>
								<div className="space-y-2.5">
									{difficultyTypeData.map((item, index) => (
										<div
											key={index}
											className="flex items-center justify-between py-2.5 px-5 bg-white border border-gray-200 rounded-lg shadow-sm"
										>
											<div className="flex flex-col">
												<span className="text-sm font-semibold text-gray-600 mb-1">
													{item.name}
												</span>
												<div className="flex items-baseline gap-1">
													<span className="text-3xl font-normal text-black">
														{item.total.value}
													</span>
													<span className="text-sm font-normal text-gray-400">
														total
													</span>
												</div>
											</div>
											<div className="flex-shrink-0">
												<DonutChart
													data={[
														{
															name: "Total",
															value: item.total.value,
															fill: item.total.fill,
														},
														{
															name: "Remaining",
															value: item.remaining.value,
															fill: item.remaining.fill,
														},
													]}
													showLegend={false}
													showTooltip={true}
													innerRadius={19}
													outerRadius={31}
													className="w-[63px] h-[63px]"
												/>
											</div>
										</div>
									))}
								</div>
							</div>

							{/* Subject Wise */}
							<div className="flex flex-col justify-between">
								<h3 className="text-sm font-semibold text-gray-600 mb-4">
									Subject Wise
								</h3>
								<div className="space-y-4">
									{subjectWiseData.map((item, index) => (
										<div key={index} className="space-y-2">
											<div className="flex justify-between items-center">
												<span className="text-sm font-medium text-[#1A1C1E]">
													{item.name}
												</span>
											</div>
											<div className="flex gap-0.5">
												<div
													className="h-4 bg-[#5936CD80] transition-all duration-300"
													style={{ width: `${item.lightValue}%` }}
												/>
												<div
													className="h-4 bg-accent transition-all duration-300"
													style={{
														width: `${item.totalValue - item.lightValue}%`,
													}}
												/>
											</div>
										</div>
									))}
								</div>
							</div>
						</div>
					</div>
				)}

				{/* Future Predictions Heading (Always Visible) */}
				<div className="mb-7">
					<h2 className="font-bold text-2xl text-[#202224]">
						Future Predictions
					</h2>
				</div>

				{/* Future Predictions */}
				{isLoading ? (
					<div className="grid grid-cols-1 lg:grid-cols-2 gap-2.5">
						<AnalyticsFutureLeftLoading />
						<AnalyticsFutureRightLoading />
					</div>
				) : analyticsError ? (
					<AnalyticsEmptyState variant="no-data" className="py-20" />
				) : !hasAnalyticsData ? (
					<AnalyticsEmptyState variant="no-tests-completed" layout="grid" />
				) : (
					<div className="grid grid-cols-1 lg:grid-cols-2 gap-2.5">
						{/* Predicted MCQs */}
						<div className="mb-9 px-6 py-7 bg-white rounded-3xl border border-gray-300 flex flex-col">
							<div className="mb-4">
								<h3 className="text-sm font-bold text-gray-400 mb-2.5">
									PREDICTED MCQ'S
								</h3>
								<p className="text-[32px] font-medium text-gray-700">
									{predictedCorrectAnswers + predictedWrongAnswers}{" "}
									<span className="text-base font-medium text-gray-400">
										predicted
									</span>
								</p>
							</div>
							<PieChart
								data={futurePredictionData}
								showLegend={true}
								innerRadius={0}
								outerRadius={70}
								className="h-full flex flex-col justify-between"
							/>
						</div>

						{/* Subject Wise Prediction */}
						<div className="mb-9 px-6 py-7 bg-white rounded-3xl border border-gray-300">
							<div className="mb-10">
								<h3 className="text-sm font-bold text-gray-400 mb-2.5">
									SUBJECT WISE PREDICTION
								</h3>
								<p className="text-[32px] font-medium text-gray-700">
									{Math.round(avgScore * 1.1) || 75}%{" "}
									<span className="text-base font-medium text-gray-400">
										predicted avg
									</span>
								</p>
							</div>
							<div className="space-y-4">
								{subjectWisePredictionData.map((item, index) => (
									<div key={index} className="space-y-2">
										<div className="flex justify-between items-center">
											<span className="text-sm font-medium text-[#1A1C1E]">
												{item.name}
											</span>
										</div>
										<div className="flex gap-0.5">
											<div
												className="h-4 bg-[#5936CD80] transition-all duration-300"
												style={{ width: `${item.lightValue}%` }}
											/>
											<div
												className="h-4 bg-accent transition-all duration-300"
												style={{
													width: `${item.totalValue - item.lightValue}%`,
												}}
											/>
										</div>
									</div>
								))}
							</div>
						</div>
					</div>
				)}
			</main>
		</>
	);
};

export const Route = createFileRoute("/(app)/_mainlayout/analytics")({
	beforeLoad: () => {},
	component: Page,
});
